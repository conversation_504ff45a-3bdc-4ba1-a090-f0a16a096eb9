// 测试数据生成器 - 用于生成大量联系人数据来测试虚拟滚动性能

import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  unreadCount: number
  user: User
  hasLastMessage?: boolean
}

// 中文姓名库
const surnames = [
  '王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴',
  '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗',
  '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧'
]

const givenNames = [
  '伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军',
  '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞',
  '平', '刚', '桂英', '建华', '文', '华', '金凤', '素英', '建国', '德华',
  '秀珍', '建军', '春梅', '海燕', '雪梅', '玉兰', '文华', '月英', '玉英', '玉华',
  '志强', '志明', '秀云', '桂花', '志华', '志刚', '秀梅', '丽娟', '丽华', '志伟'
]

// 状态选项
const statusOptions = ['在线', '离线', '忙碌', '离开']

// 示例消息内容
const sampleMessages = [
  '你好，最近怎么样？',
  '今天天气不错呢',
  '工作顺利吗？',
  '有空一起吃饭吧',
  '周末有什么安排？',
  '这个项目进展如何？',
  '谢谢你的帮助',
  '明天见面聊聊',
  '收到，马上处理',
  '好的，没问题',
  '辛苦了！',
  '注意休息',
  '路上小心',
  '祝你好运！',
  '生日快乐！',
  '新年快乐！',
  '恭喜恭喜！',
  '加油！',
  '保持联系',
  '期待合作'
]

/**
 * 生成随机中文姓名
 */
function generateRandomName(): string {
  const surname = surnames[Math.floor(Math.random() * surnames.length)]
  const givenName1 = givenNames[Math.floor(Math.random() * givenNames.length)]
  const givenName2 = Math.random() > 0.5 ? givenNames[Math.floor(Math.random() * givenNames.length)] : ''
  return surname + givenName1 + givenName2
}

/**
 * 生成随机状态
 */
function generateRandomStatus(): string {
  return statusOptions[Math.floor(Math.random() * statusOptions.length)]
}

/**
 * 生成随机消息
 */
function generateRandomMessage(): string {
  return sampleMessages[Math.floor(Math.random() * sampleMessages.length)]
}

/**
 * 生成随机时间（最近30天内）
 */
function generateRandomTime(): Date {
  const now = new Date()
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const randomTime = new Date(thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime()))
  return randomTime
}

/**
 * 获取姓名头像
 */
function getNameAvatar(name: string): string {
  return name.length >= 2 ? name.slice(-2) : name
}

/**
 * 生成测试联系人数据
 * @param count 要生成的联系人数量
 * @returns 联系人数组
 */
export function generateTestContacts(count: number): Contact[] {
  const contacts: Contact[] = []
  
  for (let i = 0; i < count; i++) {
    const name = generateRandomName()
    const status = generateRandomStatus()
    const lastMessage = generateRandomMessage()
    const lastMessageTime = generateRandomTime()
    const hasLastMessage = Math.random() > 0.1 // 90% 的联系人有最后消息
    const unreadCount = Math.random() > 0.7 ? Math.floor(Math.random() * 99) + 1 : 0 // 30% 的联系人有未读消息
    
    const user: User = {
      id: `test-user-${i.toString().padStart(6, '0')}`,
      username: `user${i}`,
      email: `user${i}@test.com`,
      displayName: name,
      avatar: '/avatars/default.png',
      isOnline: status === '在线',
      lastOnlineTime: lastMessageTime.toISOString()
    }
    
    const contact: Contact = {
      id: user.id,
      name,
      avatar: getNameAvatar(name),
      status,
      lastMessage: hasLastMessage ? lastMessage : '',
      lastMessageTime,
      unreadCount,
      user,
      hasLastMessage
    }
    
    contacts.push(contact)
  }
  
  // 按最后消息时间排序（最新的在前面）
  contacts.sort((a, b) => {
    // 有未读消息的排在前面
    if (a.unreadCount > 0 && b.unreadCount === 0) return -1
    if (a.unreadCount === 0 && b.unreadCount > 0) return 1
    
    // 按时间排序
    return b.lastMessageTime.getTime() - a.lastMessageTime.getTime()
  })
  
  return contacts
}

/**
 * 生成性能测试数据
 * @param count 联系人数量，默认1000
 */
export function generatePerformanceTestData(count: number = 1000): Contact[] {
  console.log(`🧪 生成 ${count} 个测试联系人数据...`)
  const startTime = performance.now()
  
  const contacts = generateTestContacts(count)
  
  const endTime = performance.now()
  console.log(`✅ 生成完成，耗时: ${(endTime - startTime).toFixed(2)}ms`)
  console.log(`📊 数据统计:`)
  console.log(`   - 总联系人数: ${contacts.length}`)
  console.log(`   - 有未读消息: ${contacts.filter(c => c.unreadCount > 0).length}`)
  console.log(`   - 在线用户: ${contacts.filter(c => c.status === '在线').length}`)
  console.log(`   - 有最后消息: ${contacts.filter(c => c.hasLastMessage).length}`)
  
  return contacts
}
